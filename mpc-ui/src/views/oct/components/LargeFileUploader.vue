<template>
  <div class="upload-enhanced-wrapper">
    <!-- 上传文件组件 -->
    <uploader
      class="uploader-app"
      ref="uploader"
      :options="options"
      :autoStart="false"
      @files-added="handleFilesAdded"
      @file-removed="handleFileRemoved"
      @file-progress="handleFileProgress"
      @file-success="handleFileSuccess"
      @file-error="handleFileError"
      :file-status-text="fileStatusTextObj"
    >
      <uploader-unsupport></uploader-unsupport>

      <!-- 隐藏的选择按钮 -->
      <uploader-btn class="select-file-btn" :attrs="attrs" ref="uploadBtn">选择文件</uploader-btn>
      <uploader-btn class="select-file-btn" :attrs="attrs" :directory="true" ref="uploadDirBtn">选择文件夹</uploader-btn>

      <!-- 拖拽上传区域 -->
      <uploader-drop>
        <div class="drop-box">
          <div class="upload-icon">
            <i class="el-icon-upload2"></i>
          </div>
          <div class="upload-text">
            <div class="main-text">拖拽文件或文件夹到此处上传</div>
            <div class="sub-text">
              或者
              <span class="upload-btn" @click="handleClickUploadBtn">选择文件</span>
              /
              <span class="upload-btn" @click="handleClickUploadDirBtn">选择文件夹</span>
            </div>
            <div class="upload-tips">
              支持 {{ attrs.accept }} 格式，单文件最大 {{ maxFileSize }}
            </div>
          </div>
        </div>
      </uploader-drop>

      <!-- 文件列表 -->
      <uploader-list>
        <template v-slot:default="props">
          <div v-if="filesList.length === 0" class="empty-state">
            <i class="el-icon-document"></i>
            <p>暂无文件，请选择文件或拖拽文件到上方区域</p>
          </div>

          <div class="file-panel" v-if="filesList.length > 0">
            <!-- 头部操作栏 -->
            <div class="panel-header">
              <div class="header-left">
                <span class="title">上传列表</span>
                <span class="count">({{ filesList.length }}个文件)</span>
                <span class="total-size">总大小: {{ formatFileSize(totalSize) }}</span>
              </div>
              <div class="header-right">
                <el-button
                  size="mini"
                  type="primary"
                  @click="startUpload"
                  :disabled="filesList.length === 0 || isUploading"
                  :loading="isUploading"
                >
                  <i class="el-icon-upload"></i> 开始上传
                </el-button>
                <el-button
                  size="mini"
                  @click="clearAllFiles"
                  :disabled="isUploading"
                >
                  清空列表
                </el-button>
              </div>
            </div>

            <!-- 文件树结构 -->
            <div class="file-tree">
              <file-tree-node
                v-for="(node, key) in fileTree"
                :key="`${key}-${fileTreeVersion}`"
                :node="node"
                :node-key="key"
                :level="0"
                @toggle-folder="toggleFolder"
                @remove-file="onFileRemove"
                @retry-file="onRetry"
              />
            </div>
          </div>
        </template>
      </uploader-list>
    </uploader>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { getToken } from "@/utils/auth";
import { mergeChunks } from "@/api/file/file";
import { createInfo, updateFileInfoStatus } from "@/api/oct/index";
import FileTreeNode from './FileTreeNode.vue';

// 常量定义
const CHUNK_SIZE = 20 * 1024 * 1024 // 每个分片的大小：20MB
const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024 // 最大文件大小：2GB
const ALLOWED_EXTENSIONS = ['.zip', '.rar'] // 允许的文件扩展名





export default {
  name: 'UploadLargeFilesEnhanced',
  components: {
    FileTreeNode
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 上传组件配置项
      options: {
        target: process.env.VUE_APP_BASE_API + "/file/chunk",
        chunkSize: CHUNK_SIZE,
        fileParameterName: 'file',
        maxChunkRetries: 1,
        testChunks: false, // 禁用自动测试，改为手动控制
        headers: this.getAuthHeaders(),
        query: this.formData,
        checkChunkUploadedByResponse: this.checkChunkUploaded,
        parseTimeRemaining: this.parseTimeRemaining,
        processParams: (params) => params
      },
      attrs: {
        accept: ALLOWED_EXTENSIONS.join(', ')
      },
      fileStatusTextObj: {
        success: "上传成功",
        error: "上传失败",
        uploading: "正在上传",
        paused: "已暂停",
        waiting: "等待中",
      },
      filesList: [],
      fileTree: {}, // 文件树结构
      fileTreeVersion: 0, // 用于强制更新文件树
      isUploading: false,
      uploadProgress: {
        total: 0,
        completed: 0,
        failed: 0
      }
    }
  },
  computed: {
    uploaderInstance() {
      return this.$refs.uploader?.uploader
    },
    totalSize() {
      return this.filesList.reduce((total, file) => total + (file.size || 0), 0)
    },
    maxFileSize() {
      return this.formatFileSize(MAX_FILE_SIZE)
    }
  },
  methods: {
    // 获取认证头
    getAuthHeaders() {
      const token = getToken()
      return token ? { Authorization: `Bearer ${token}` } : {}
    },

    // 检查分片是否已上传
    checkChunkUploaded(chunk, response) {
      try {
        const result = JSON.parse(response)
        if (result.code && result.code !== 200) {
          chunk.uploader.pause()
        }
        return (result.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
      } catch (error) {
        console.error('解析分片响应失败:', error)
        return false
      }
    },

    // 解析剩余时间
    parseTimeRemaining(_timeRemaining, parsedTimeRemaining) {
      return parsedTimeRemaining
        .replace(/\syears?/, "年")
        .replace(/\days?/, "天")
        .replace(/\shours?/, "小时")
        .replace(/\sminutes?/, "分钟")
        .replace(/\sseconds?/, "秒")
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    },

    // 验证文件
    validateFile(file) {
      // 检查文件大小
      if (file.size > MAX_FILE_SIZE) {
        this.$message.error(`文件 "${file.name}" 超过最大限制 ${this.maxFileSize}`)
        return false
      }

      // 检查文件类型
      const ext = '.' + file.name.split('.').pop().toLowerCase()
      if (!ALLOWED_EXTENSIONS.includes(ext)) {
        this.$message.error(`文件 "${file.name}" 格式不支持，仅支持 ${ALLOWED_EXTENSIONS.join(', ')} 格式`)
        return false
      }

      return true
    },

    handleClickUploadBtn() {
      this.$refs.uploadBtn.$el.click()
    },
    handleClickUploadDirBtn() {
      this.$refs.uploadDirBtn.$el.click()
    },
    
    // 构建文件树结构
    buildFileTree(files) {
      const tree = {};
      
      files.forEach(file => {
        const path = file.relativePath || file.name;
        const parts = path.split('/').filter(part => part);
        
        let current = tree;
        
        // 构建文件夹路径
        for (let i = 0; i < parts.length - 1; i++) {
          const folderName = parts[i];
          if (!current[folderName]) {
            current[folderName] = {
              type: 'folder',
              name: folderName,
              expanded: true,
              children: {}
            };
          }
          current = current[folderName].children;
        }
        
        // 添加文件
        const fileName = parts[parts.length - 1];
        current[fileName] = {
          type: 'file',
          name: fileName,
          file: file,
          progress: 0,
          status: 'waiting',
          statusText: '等待中',
          md5Computing: false
        };
      });
      
      return tree;
    },

    // 更新文件状态
    updateFileInTree(file, updates) {
      const findAndUpdate = (tree) => {
        for (const key in tree) {
          const node = tree[key];
          if (node.type === 'file' && node.file.id === file.id) {
            Object.assign(node, updates);
            return true;
          } else if (node.type === 'folder' && node.children) {
            if (findAndUpdate(node.children)) return true;
          }
        }
        return false;
      };

      if (findAndUpdate(this.fileTree)) {
        // 通过更新版本号触发响应式更新
        this.fileTreeVersion++
      }
    },

    handleFilesAdded(files) {
      // 过滤和验证文件
      const validFiles = files.filter(file => this.validateFile(file))

      if (validFiles.length === 0) {
        return
      }

      validFiles.forEach(file => {
        // 为新添加的文件设置初始状态
        file.status = 'waiting'
        file.statusText = '等待上传'
        file.progress = 0

        // 强制清除可能影响手动上传判断的属性
        file.uniqueIdentifier = undefined
        file.completed = false

        // 如果文件有重置方法，调用它
        if (file.reset && typeof file.reset === 'function') {
          file.reset()
        }
      })

      this.filesList = [...this.filesList, ...validFiles]
      this.$emit('fileList', this.filesList)

      // 重新构建文件树
      this.fileTree = this.buildFileTree(this.filesList)
      this.fileTreeVersion++

      // 显示添加成功提示
      if (validFiles.length > 0) {
        this.$message.success(`成功添加 ${validFiles.length} 个文件`)
      }
    },

    handleFileRemoved(file) {
      this.filesList = this.filesList.filter((item) => item.id !== file.id)
      this.$emit('fileList', this.filesList)

      // 重新构建文件树
      this.fileTree = this.buildFileTree(this.filesList)
      this.fileTreeVersion++
    },

    // 处理文件上传进度
    handleFileProgress(_rootFile, file, _chunk) {
      let progressValue = 0;
      if (typeof file.progress === 'function') {
        progressValue = file.progress();
      } else if (typeof file.progress === 'number') {
        progressValue = file.progress;
      }

      const progress = Math.floor(progressValue * 100);

      // 初始化上传开始时间
      if (!file.timeOfFirstUpload) {
        file.timeOfFirstUpload = Date.now();
      }

      // 计算上传速度
      const currentTime = Date.now();
      const timeElapsed = (currentTime - file.timeOfFirstUpload) / 1000; // 秒
      const bytesUploaded = file.size * progressValue;
      const averageSpeed = timeElapsed > 0 ? bytesUploaded / timeElapsed : 0;

      // 计算剩余时间
      const remainingBytes = file.size - bytesUploaded;
      const timeRemaining = averageSpeed > 0 ? remainingBytes / averageSpeed : 0;

      // 更新文件状态
      this.updateFileInTree(file, {
        status: 'uploading',
        progress: progress,
        averageSpeed: averageSpeed,
        timeRemaining: timeRemaining,
        statusText: '正在上传'
      });
    },

    toggleFolder(nodeKey) {
      const findAndToggle = (tree, key) => {
        if (tree[key] && tree[key].type === 'folder') {
          tree[key].expanded = !tree[key].expanded;
          return true;
        }
        for (const treeKey in tree) {
          if (tree[treeKey].type === 'folder' && tree[treeKey].children) {
            if (findAndToggle(tree[treeKey].children, key)) return true;
          }
        }
        return false;
      };
      
      if (findAndToggle(this.fileTree, nodeKey)) {
        this.fileTreeVersion++
      }
    },

    async computeMD5(file) {
      return new Promise((resolve) => {
        this.updateFileInTree(file, { 
          statusText: '分片处理中 0%', 
          md5Computing: true 
        });
        
        let fileReader = new FileReader()
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
        let currentChunk = 0
        let chunks = Math.ceil(file.size / CHUNK_SIZE)
        let spark = new SparkMD5.ArrayBuffer()
        
        // 在手动上传模式下，不需要暂停文件
        // file.pause()
        loadNext()
        
        fileReader.onload = (e) => {
          spark.append(e.target.result)
          if (currentChunk < chunks) {
            currentChunk++
            loadNext()
            this.updateFileInTree(file, { 
              statusText: `分片处理中 ${((currentChunk / chunks) * 100).toFixed(0)}%` 
            });
          } else {
            let md5 = spark.end()
            file.uniqueIdentifier = md5
            
            let params = {
              identifier: md5,
              manufacturerInfoId: this.formData.manufacturerInfoId,
              deviceTypeId: this.formData.deviceTypeId,
              fileSize: file.size,
              fileName: file.name
            }
            
            createInfo(params).then(res => {
              if (res.data.status == 1) {
                this.$message.warning('影像已存在，请勿重复上传')
                file.cancel()
              } else {
                this.updateFileInTree(file, { 
                  statusText: '等待上传', 
                  md5Computing: false 
                });
                file.resume()
                resolve()
              }
            })
          }
        }
        
        fileReader.onerror = () => {
          this.$message.error(`文件${file.name}读取出错，请检查该文件`)
          file.cancel()
          resolve()
        }
        
        function loadNext() {
          let start = currentChunk * CHUNK_SIZE
          let end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE
          fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        }
      })
    },

    handleFileSuccess(_rootFile, file, response) {
      // _rootFile 参数保留以兼容 uploader 组件接口
      let result = response ? JSON.parse(response) : ''
      
      if (result.code == 200) return
      
      const formData = new FormData();
      formData.append("identifier", file.uniqueIdentifier);
      formData.append("filename", file.name);
      formData.append("relativePath", file.relativePath);
      formData.append("totalSize", file.size);
      
      mergeChunks(formData).then((res) => {
        this.updateFileInTree(file, { 
          status: 'success', 
          statusText: '上传成功',
          progress: 100
        });
        
        if (Notification.permission === 'granted') {
          new Notification('文件上传完成', { body: file.name })
        }
        
        let params = {
          identifier: file.uniqueIdentifier,
          status: 1,
          fileUrl: res.data.fileUrl
        }
        updateFileInfoStatus(params)
      })
    },

    handleFileError(_rootFile, file, response) {
      // _rootFile 参数保留以兼容 uploader 组件接口
      console.error('文件上传失败:', file.name, response);

      // 解析错误信息
      let errorMessage = '上传失败';
      let detailMessage = '';

      try {
        if (response) {
          const errorData = typeof response === 'string' ? JSON.parse(response) : response;
          if (errorData.message) {
            detailMessage = errorData.message;
          } else if (errorData.msg) {
            detailMessage = errorData.msg;
          } else if (errorData.error) {
            detailMessage = errorData.error;
          }
        }
      } catch (e) {
        detailMessage = response || '网络错误';
      }

      // 更新文件状态
      this.updateFileInTree(file, {
        status: 'error',
        statusText: errorMessage,
        errorDetail: detailMessage
      });

      // 显示错误提示
      const fileName = file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name;
      this.$message.error(`文件 "${fileName}" 上传失败${detailMessage ? '：' + detailMessage : ''}`);
    },

    // 开始所有上传
    async startAllUpload() {
      if (this.uploaderInstance.isUploading()) return
      this.isUploading = true;
      
      for (let i = 0; i < this.filesList.length; i++) {
        let file = this.filesList[i]
        if (!file.completed) {
          await this.computeMD5(file)
        }
      }
    },

    // 暂停所有上传
    pauseAllUpload() {
      this.uploaderInstance.pause();
      this.isUploading = false;
    },

    // 手动开始上传
    async startUpload() {
      if (this.filesList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      if (this.isUploading) {
        this.$message.warning('正在上传中，请稍候')
        return
      }

      this.isUploading = true

      try {
        // 启动上传器
        if (this.uploaderInstance) {
          this.uploaderInstance.upload()
        }

        // 为所有等待上传的文件开始MD5计算
        for (let i = 0; i < this.filesList.length; i++) {
          const file = this.filesList[i]

          // 检查条件
          const hasUniqueId = !!file.uniqueIdentifier
          const isCompleted = !!file.completed
          const isFileComplete = file.isComplete ? file.isComplete() : false

          if (!hasUniqueId && !isCompleted && !isFileComplete) {
            await this.computeMD5(file)
          }
        }
      } catch (error) {
        console.error('上传过程中出现错误:', error)
        this.$message.error('上传过程中出现错误，请重试')
        this.isUploading = false
      }
    },

    // 清空所有文件
    clearAllFiles() {
      if (this.uploaderInstance) {
        this.uploaderInstance.cancel()
      }
      this.filesList = []
      this.fileTree = {}
      this.fileTreeVersion++
      this.isUploading = false
      this.$emit('fileList', this.filesList)
      this.$message.success('已清空文件列表')
    },

    onFileRemove(file) {
      file.cancel()
    },

    onRetry(file) {
      file.retry()
    },
    onPause(file) {
      file.pause()
      this.updateFileInTree(file, { status: 'paused', statusText: '已暂停' });
    },
    onResume(file) {
      file.resume()
      this.updateFileInTree(file, { status: 'uploading', statusText: '正在上传' });
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-enhanced-wrapper {
  .uploader-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  // 拖拽上传区域
  .drop-box {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    text-align: center;
    padding: 40px 20px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    .upload-icon {
      margin-bottom: 16px;

      .el-icon-upload2 {
        font-size: 48px;
        color: #d9d9d9;
      }
    }

    .upload-text {
      .main-text {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
      }

      .sub-text {
        font-size: 14px;
        color: #666;

        .upload-btn {
          color: #1890ff;
          cursor: pointer;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .upload-tips {
        font-size: 12px;
        color: #999;
        margin-top: 8px;
      }
    }
  }

  // 空状态样式
  .empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #999;

    .el-icon-document {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // 文件面板
  .file-panel {
    margin-top: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fff;
    overflow: hidden;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f5f5f5;
      border-bottom: 1px solid #e8e8e8;

      .header-left {
        display: flex;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-right: 8px;
        }

        .count {
          font-size: 14px;
          color: #666;
          margin-right: 12px;
        }

        .total-size {
          font-size: 12px;
          color: #999;
          background: #f5f5f5;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }

      .header-right {
        .el-button {
          margin-left: 8px;
        }
      }
    }

    .file-tree {
      max-height: 400px;
      overflow-y: auto;
    }
  }

  // 文件树节点
  .tree-node {
    .node-content {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      &.is-folder {
        font-weight: 500;
      }

      .folder-toggle {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        cursor: pointer;
        color: #666;
        transition: transform 0.2s ease;

        &:hover {
          color: #1890ff;
        }
      }

      .file-indent {
        width: 20px;
        height: 16px;
      }

      .node-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #1890ff;

        &.el-icon-folder {
          color: #faad14;
        }

        &.el-icon-box {
          color: #722ed1;
        }

        &.el-icon-picture {
          color: #52c41a;
        }

        &.el-icon-video-camera {
          color: #f5222d;
        }

        &.el-icon-headset {
          color: #13c2c2;
        }
      }

      .node-name {
        flex: 0 0 auto;
        margin-right: 16px;
        font-size: 14px;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .file-size {
          flex: 0 0 80px;
          font-size: 12px;
          color: #999;
          margin-right: 16px;
        }

        .progress-container {
          flex: 1;
          margin-right: 16px;
          min-width: 120px;

          ::v-deep .el-progress-bar {
            padding-right: 0;
          }

          ::v-deep .el-progress-bar__outer {
            background-color: #f0f0f0;
          }

          ::v-deep .el-progress-bar__inner {
            background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
          }
        }

        .status-info {
          flex: 0 0 80px;
          margin-right: 16px;

          .status-text {
            font-size: 12px;
            font-weight: 500;
          }
        }

        .file-actions {
          flex: 0 0 auto;
          display: flex;
          gap: 4px;

          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            line-height: 1;
          }
        }
      }
    }
  }

  // 隐藏原始上传按钮
  .select-file-btn {
    display: none;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .panel-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .header-right {
        display: flex;
        justify-content: center;
        gap: 8px;

        .el-button {
          margin: 0;
          flex: 1;
        }
      }
    }

    .node-content {
      .file-info {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .file-size,
        .status-info {
          flex: none;
        }

        .progress-container {
          margin: 0;
        }

        .file-actions {
          justify-content: center;
        }
      }
    }
  }
}

// 全局样式覆盖
::v-deep {
  .uploader-drop {
    border: none !important;
    background: none !important;
  }

  .uploader-drop-active {
    .drop-box {
      border-color: #1890ff !important;
      background: #e6f7ff !important;
    }
  }
}
</style>
